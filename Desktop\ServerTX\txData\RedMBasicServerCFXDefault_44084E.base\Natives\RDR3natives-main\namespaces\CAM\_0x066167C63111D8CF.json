{"0x066167C63111D8CF": {"name": "_SET_GAMEPLAY_CAM_PARAMS_THIS_UPDATE", "comment": "Not official name\nThis native allows to move the gameplay cam with the given speed, enableSlide will allow to move the cam to the slideOffset, enable<PERSON><PERSON> will allow to move the cam to the zoomOffset. \n\nEXAMPLE:\nCitizen.InvokeNative(0x066167c63111d8cf,1.0, true, -1.0, true, 2.0)", "params": [{"type": "float", "name": "camSpeed"}, {"type": "BOOL", "name": "enableSlide"}, {"type": "float", "name": "slideOffset"}, {"type": "BOOL", "name": "enableZoom"}, {"type": "float", "name": "zoomOffset"}], "return_type": "void", "build": "1207", "examples": [], "apiset": ""}}