{"0x61914209C36EFDDB": {"name": "GET_CARRIABLE_ENTITY_STATE", "comment": "enum eCarriableState\n{\n\tCAR<PERSON><PERSON>LE_STATE_NONE,\n\t<PERSON><PERSON><PERSON><PERSON>LE_STATE_TRANSITIONING_TO_HOGTIED,\n\tCAR<PERSON>ABLE_STATE_CARRIABLE_INTRO,\n\tCA<PERSON><PERSON><PERSON><PERSON>_STATE_CARRIABLE,\n\tCAR<PERSON><PERSON>LE_STATE_BEING_PICKED_UP_FROM_GROUND,\n\tCAR<PERSON>ABLE_STATE_CARRIED_BY_HUMAN,\n\tCAR<PERSON>ABLE_STATE_BEING_PLACED_ON_GROUND,\n\tCAR<PERSON>ABLE_STATE_CARRIED_BY_MOUNT,\n\tCARRIABLE_STATE_BEING_PLACED_ON_MOUNT,\n\tCARRIABLE_STATE_BEING_PICKED_UP_FROM_MOUNT,\n\tCAR<PERSON><PERSON>LE_STATE_BEING_CUT_FREE,\n\tCARRIABLE_STATE_BEING_PLACED_ON_GROUND_ESCAPE,\n\tCARRIABLE_STATE_BEING_PLACED_IN_VEHIC<PERSON>\n};", "params": [{"type": "Entity", "name": "entity"}], "return_type": "int", "build": "1207", "examples": [], "apiset": ""}}