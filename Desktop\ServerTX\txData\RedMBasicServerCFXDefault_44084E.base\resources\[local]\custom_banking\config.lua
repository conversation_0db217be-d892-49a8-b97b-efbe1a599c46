Config = {}

-- <PERSON><PERSON><PERSON><PERSON><PERSON>
Config.Lang = 'sv' -- 'en', 'sv'

-- Databas
Config.Database = {
    table = 'users', -- <PERSON> anvä<PERSON>bell
    moneyColumn = 'money',
    bankColumn = 'bank',
    identifierColumn = 'identifier'
}

-- Banker
Config.Banks = {
    {
        name = "Valentine Bank",
        coords = vector3(-308.02, 773.82, 116.7),
        heading = 18.69,
        blip = {
            sprite = -**********,
            scale = 0.8,
            color = 'WHITE'
        },
        npc = {
            model = "S_M_M_BankClerk_01",
            coords = vector4(-308.02, 773.82, 116.7, 18.69)
        },
        hours = {
            open = 7,
            close = 22
        }
    },
    {
        name = "Blackwater Bank", 
        coords = vector3(-813.18, -1277.60, 43.68),
        heading = 180.0,
        blip = {
            sprite = -**********,
            scale = 0.8,
            color = 'WHITE'
        },
        npc = {
            model = "S_M_M_BankClerk_01",
            coords = vector4(-813.18, -1277.60, 43.68, 180.0)
        },
        hours = {
            open = 7,
            close = 22
        }
    }
}

-- Inställningar
Config.Settings = {
    transferFee = 0.05, -- 5% avgift för överföringar
    maxTransfer = 10000, -- Max överföring per gång
    interactionDistance = 3.0,
    currency = '$'
}

-- Språk
Config.Locales = {
    ['en'] = {
        ['bank_menu'] = 'Bank Menu',
        ['deposit'] = 'Deposit Money',
        ['withdraw'] = 'Withdraw Money',
        ['transfer'] = 'Transfer Money',
        ['balance'] = 'Balance: %s',
        ['cash'] = 'Cash: %s',
        ['enter_amount'] = 'Enter amount',
        ['enter_player_id'] = 'Enter player ID',
        ['insufficient_funds'] = 'Insufficient funds',
        ['invalid_amount'] = 'Invalid amount',
        ['transaction_success'] = 'Transaction successful',
        ['bank_closed'] = 'Bank is closed. Open hours: %s:00 - %s:00'
    },
    ['sv'] = {
        ['bank_menu'] = 'Bank Meny',
        ['deposit'] = 'Sätt in pengar',
        ['withdraw'] = 'Ta ut pengar', 
        ['transfer'] = 'Överför pengar',
        ['balance'] = 'Saldo: %s',
        ['cash'] = 'Kontanter: %s',
        ['enter_amount'] = 'Ange belopp',
        ['enter_player_id'] = 'Ange spelare ID',
        ['insufficient_funds'] = 'Otillräckliga medel',
        ['invalid_amount'] = 'Ogiltigt belopp',
        ['transaction_success'] = 'Transaktion lyckades',
        ['bank_closed'] = 'Banken är stängd. Öppettider: %s:00 - %s:00'
    }
}

function _L(key, ...)
    local locale = Config.Locales[Config.Lang] or Config.Locales['en']
    local text = locale[key] or key
    return string.format(text, ...)
end
