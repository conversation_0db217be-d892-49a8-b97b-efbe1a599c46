{"0x1392105DA88BBFFB": {"name": "_ADD_PROP_TO_MINIMAP", "comment": "list of minimap props: https://github.com/femga/rdr3_discoveries/tree/master/graphics/minimap/minimapObjects\nvariations parameter are the interior locations you see on the map like these bellow\nvariation 0 https://i.imgur.com/jkLhn3Z.png\nvariation 2  https://i.imgur.com/eKV0Tcm.png\nvariation 4 https://i.imgur.com/rjwOgEH.png\nthere are more and you can find them in the decompiles", "params": [{"type": "Hash", "name": "minimapProp"}, {"type": "float", "name": "x"}, {"type": "float", "name": "y"}, {"type": "float", "name": "rotation"}, {"type": "int", "name": "variation"}], "return_type": "void", "build": "1207", "examples": [], "apiset": ""}}