fx_version 'cerulean'
game 'rdr3'
rdr3_warning 'I acknowledge that this is a prerelease build of RedM, and I am aware my resources *will* become incompatible once RedM ships.'

author 'Crow RP Team'
description 'Crow Framework - Core System'
version '1.0.0'

shared_scripts {
    'config.lua',
    'shared/utils.lua',
    'shared/events.lua'
}

client_scripts {
    'client/main.lua',
    'client/player.lua',
    'client/ui.lua',
    'client/notifications.lua'
}

server_scripts {
    '@oxmysql/lib/MySQL.lua',
    'server/main.lua',
    'server/player.lua',
    'server/database.lua',
    'server/commands.lua'
}

ui_page 'html/index.html'

files {
    'html/index.html',
    'html/style.css',
    'html/script.js',
    'html/notifications.css',
    'html/notifications.js'
}

exports {
    'GetPlayerData',
    'GetPlayerMoney',
    'AddMoney',
    'RemoveMoney',
    'ShowNotification',
    'GetFrameworkObject'
}

server_exports {
    'GetPlayer',
    'GetPlayerByIdentifier',
    'AddMoney',
    'RemoveMoney',
    'CreateLog'
}
