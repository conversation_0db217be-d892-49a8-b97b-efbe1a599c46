{"0x2A488C176D52CCA5": {"name": "VDIST", "comment": "Calculates distance between vectors.\nThe value returned will be in meters.", "params": [{"type": "float", "name": "x1"}, {"type": "float", "name": "y1"}, {"type": "float", "name": "z1"}, {"type": "float", "name": "x2"}, {"type": "float", "name": "y2"}, {"type": "float", "name": "z2"}], "return_type": "float", "build": "1207", "gta_hash": "0x2A488C176D52CCA5", "gta_jhash": "0x3C08ECB7", "examples": [], "apiset": ""}}