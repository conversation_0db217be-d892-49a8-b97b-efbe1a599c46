{"0xE2487779957FE897": {"name": "_SET_TRANSPORT_USAGE_FLAGS", "comment": "enum eTransportUsageFlags\n{\n\tTUF_INVALID = 0,\n\tTUF_ALLOW_DRIVER_ME = (1 << 0),\n\tTUF_ALLOW_DRIVER_GANG = (1 << 1),\n\tTUF_ALLOW_DRIVER_CREW = (1 << 2),\n\tTUF_ALLOW_DRIVER_FRIENDS = (1 << 3),\n\tTUF_ALLOW_DRIVER_ANYONE = (1 << 4),\n\tTUF_ALLOW_PASSENGER_ME = (1 << 5),\n\tTUF_ALLOW_PASSENGER_GANG = (1 << 6),\n\tTUF_ALLOW_PASSENGER_CREW = (1 << 7),\n\tTUF_ALLOW_PASSENGER_FRIENDS = (1 << 8),\n\tTUF_ALLOW_PASSENGER_ANYONE = (1 << 9),\n\tTUF_ALLOW_ACCESS_AI = (1 << 10)\n};", "params": [{"type": "Entity", "name": "transportEntity"}, {"type": "int", "name": "flags"}], "return_type": "void", "build": "1207", "examples": [], "apiset": ""}}