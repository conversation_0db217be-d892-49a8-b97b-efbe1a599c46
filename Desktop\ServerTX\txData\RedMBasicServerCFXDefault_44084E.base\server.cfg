# ========================================================
# Crow RP - RedM Server Configuration (Local Development)
# ========================================================

# --------- IDENTITET OCH PROJEKTINFO ---------
sv_hostname "Crow RP built with RedM Basic Server!"
sets sv_projectName "[RedM Basic Server] Crow RP"
sets sv_projectDesc "Recipe for the base resources required to run a minimal RedM server."
sets tags "default, deployer"
sets locale "root-AQ"

# --------- LICENSNYCKEL (obligatorisk) ---------
sv_licenseKey "cfxk_vECtUPQoA9a7iZ8Jddkj_3LXOnS"

# --------- SERVERINST�LLNINGAR ---------
set gamename rdr3
sv_enforceGameBuild 1491  # September 2022 update
sv_maxclients 48

# --------- NETVERKSPORTAR ---------
endpoint_add_tcp "0.0.0.0:30120"
endpoint_add_udp "0.0.0.0:30120"
set steam_webApiKey "4F987EF9A393EC295D812A8AC4D9BEBE"
set resources_useSystemChat true

# --------- DATABASE / oxmysql ---------
set mysql_connection_string "mysql://zap1033931-1:<EMAIL>/zap1033931-1?charset=utf8mb4"

# --------- RESURSER SOM SKA STARTAS ---------

# Databasdrivrutin
ensure oxmysql

# Standardresurser
ensure mapmanager
ensure chat
ensure spawnmanager
ensure sessionmanager-rdr3
ensure redm-map-one
ensure hardcap

# Voice Chat
ensure pma-voice
ensure pma-display

# Dependencies
ensure weathersync
ensure redm-ipls
ensure syn_minigame
ensure lockpick
ensure PolyZone
ensure moonshine_interiors
ensure female_body_fix

# Crow Framework
ensure crow_core

# --------- ADMINR�TTIGHETER ---------
add_ace group.admin command allow                      # Till�t alla kommandon
add_ace group.admin command.quit deny                  # Men neka 'quit'
add_principal identifier.fivem:7734419 group.admin     # Linkans (FiveM ID)
add_principal identifier.discord:232497359454601216 group.admin # Linkans (Discord ID)

# --------- VALFRITT: LOGGNING ---------
# set logfile "server.log"
