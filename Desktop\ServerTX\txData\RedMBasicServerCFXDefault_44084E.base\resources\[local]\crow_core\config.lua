Config = {}

-- Framework Information
Config.FrameworkName = "Crow Framework"
Config.Version = "1.0.0"
Config.Debug = true

-- Database Configuration
Config.Database = {
    -- Användartabell
    UsersTable = 'users',
    Columns = {
        identifier = 'identifier',
        firstname = 'firstname', 
        lastname = 'lastname',
        money = 'money',
        bank = 'bank',
        job = 'job',
        job_grade = 'job_grade',
        group = 'group',
        position = 'position',
        skin = 'skin',
        loadout = 'loadout'
    }
}

-- Pengar och ekonomi
Config.Economy = {
    StartingMoney = 500,      -- Startpengar kontant
    StartingBank = 5000,      -- Startpengar bank
    MaxMoney = *********,     -- Max pengar
    CurrencySymbol = '$'
}

-- Jobb system
Config.Jobs = {
    unemployed = {
        label = 'Arbetslös',
        grades = {
            [0] = { name = 'Arbetslös', salary = 50 }
        }
    },
    police = {
        label = 'Sheriff',
        grades = {
            [0] = { name = 'Deputy', salary = 150 },
            [1] = { name = 'Sheriff', salary = 200 },
            [2] = { name = 'Marshal', salary = 250 }
        }
    },
    doctor = {
        label = 'Läkare',
        grades = {
            [0] = { name = 'Praktikant', salary = 100 },
            [1] = { name = 'Läkare', salary = 180 },
            [2] = { name = 'Överläkare', salary = 220 }
        }
    }
}

-- Grupper/Behörigheter
Config.Groups = {
    user = {
        label = 'Användare',
        inherit = false
    },
    mod = {
        label = 'Moderator', 
        inherit = 'user'
    },
    admin = {
        label = 'Admin',
        inherit = 'mod'
    },
    superadmin = {
        label = 'Superadmin',
        inherit = 'admin'
    }
}

-- Spawn platser
Config.SpawnPoints = {
    {
        name = "Valentine",
        coords = vector4(-279.46, 804.63, 119.38, 0.0),
        model = "mp_male"
    },
    {
        name = "Blackwater", 
        coords = vector4(-856.77, -1284.73, 43.68, 0.0),
        model = "mp_male"
    },
    {
        name = "Saint Denis",
        coords = vector4(2635.47, -1224.93, 53.38, 0.0),
        model = "mp_male"
    }
}

-- Notifikationer
Config.Notifications = {
    Position = 'top-right', -- top-left, top-right, bottom-left, bottom-right
    Duration = 5000,         -- millisekunder
    MaxNotifications = 5
}

-- Språk
Config.Locale = 'sv'
Config.Locales = {
    ['sv'] = {
        -- Allmänt
        ['welcome'] = 'Välkommen till %s!',
        ['player_connected'] = '%s har anslutit till servern',
        ['player_disconnected'] = '%s har lämnat servern',
        
        -- Pengar
        ['received_money'] = 'Du fick %s%d',
        ['lost_money'] = 'Du förlorade %s%d',
        ['not_enough_money'] = 'Du har inte tillräckligt med pengar',
        ['invalid_amount'] = 'Ogiltigt belopp',
        
        -- Jobb
        ['job_set'] = 'Ditt jobb har ändrats till %s',
        ['salary_received'] = 'Du fick din lön: %s%d',
        
        -- Fel
        ['player_not_found'] = 'Spelare hittades inte',
        ['command_no_permission'] = 'Du har inte behörighet för detta kommando',
        ['unknown_error'] = 'Ett okänt fel uppstod'
    },
    ['en'] = {
        -- General
        ['welcome'] = 'Welcome to %s!',
        ['player_connected'] = '%s connected to the server',
        ['player_disconnected'] = '%s left the server',
        
        -- Money
        ['received_money'] = 'You received %s%d',
        ['lost_money'] = 'You lost %s%d', 
        ['not_enough_money'] = 'You don\'t have enough money',
        ['invalid_amount'] = 'Invalid amount',
        
        -- Jobs
        ['job_set'] = 'Your job has been changed to %s',
        ['salary_received'] = 'You received your salary: %s%d',
        
        -- Errors
        ['player_not_found'] = 'Player not found',
        ['command_no_permission'] = 'You don\'t have permission for this command',
        ['unknown_error'] = 'An unknown error occurred'
    }
}

-- Hjälpfunktion för översättningar
function _L(key, ...)
    local locale = Config.Locales[Config.Locale] or Config.Locales['en']
    local text = locale[key] or key
    return string.format(text, ...)
end
