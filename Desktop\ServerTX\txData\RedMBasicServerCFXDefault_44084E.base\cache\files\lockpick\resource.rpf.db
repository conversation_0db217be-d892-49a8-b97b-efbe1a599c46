[{"n": "C:/Users/<USER>/Desktop/ServerTX/txData/RedMBasicServerCFXDefault_44084E.base/resources//[standalone]/lockpick/client.lua", "mt": 1749372439, "s": 635, "i": "MPUSAAAAJgAAAAAAAAAAAA=="}, {"n": "C:/Users/<USER>/Desktop/ServerTX/txData/RedMBasicServerCFXDefault_44084E.base/resources//[standalone]/lockpick/fxmanifest.lua", "mt": 1749372439, "s": 363, "i": "M/USAAAAGAAAAAAAAAAAAA=="}, {"n": "C:/Users/<USER>/Desktop/ServerTX/txData/RedMBasicServerCFXDefault_44084E.base/resources//[standalone]/lockpick/html/img/lock.png", "mt": 1749372439, "s": 172625, "i": "OfUSAAAAJAAAAAAAAAAAAA=="}, {"n": "C:/Users/<USER>/Desktop/ServerTX/txData/RedMBasicServerCFXDefault_44084E.base/resources//[standalone]/lockpick/html/img/pick.png", "mt": 1749372439, "s": 20790, "i": "OPUSAAAAJgAAAAAAAAAAAA=="}, {"n": "C:/Users/<USER>/Desktop/ServerTX/txData/RedMBasicServerCFXDefault_44084E.base/resources//[standalone]/lockpick/html/index.html", "mt": 1749372439, "s": 802, "i": "OvUSAAAAJwAAAAAAAAAAAA=="}, {"n": "C:/Users/<USER>/Desktop/ServerTX/txData/RedMBasicServerCFXDefault_44084E.base/resources//[standalone]/lockpick/html/script.js", "mt": 1749372439, "s": 8059, "i": "NfUSAAAAIgAAAAAAAAAAAA=="}, {"n": "C:/Users/<USER>/Desktop/ServerTX/txData/RedMBasicServerCFXDefault_44084E.base/resources//[standalone]/lockpick/html/styles.css", "mt": 1749372439, "s": 2511, "i": "NPUSAAAAJgAAAAAAAAAAAA=="}]