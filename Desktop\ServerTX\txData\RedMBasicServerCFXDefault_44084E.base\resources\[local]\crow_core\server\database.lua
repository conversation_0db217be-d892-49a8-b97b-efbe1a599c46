-- Crow Framework - Database Management

CrowDatabase = {}

-- Skapa databastabeller
function CrowDatabase.CreateTables()
    CrowUtils.Log('info', 'Creating database tables...')

    -- Användartabell
    MySQL.Async.execute([[
        CREATE TABLE IF NOT EXISTS `users` (
            `identifier` varchar(50) NOT NULL,
            `firstname` varchar(50) NOT NULL DEFAULT '',
            `lastname` varchar(50) NOT NULL DEFAULT '',
            `money` int(11) NOT NULL DEFAULT 500,
            `bank` int(11) NOT NULL DEFAULT 5000,
            `job` varchar(50) NOT NULL DEFAULT 'unemployed',
            `job_grade` int(11) NOT NULL DEFAULT 0,
            `group` varchar(50) NOT NULL DEFAULT 'user',
            `position` longtext DEFAULT NULL,
            `skin` longtext DEFAULT NULL,
            `loadout` longtext DEFAULT NULL,
            `gender` varchar(10) DEFAULT NULL,
            `character_created` tinyint(1) NOT NULL DEFAULT 0,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `last_seen` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`identifier`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ]], {}, function(result)
        if result then
            CrowUtils.Log('info', 'Users table created/verified')
            -- Run migration to add new columns if they don't exist
            CrowDatabase.MigrateDatabase()
        else
            CrowUtils.Log('error', 'Failed to create users table')
        end
    end)
    
    -- Loggtabell
    MySQL.Async.execute([[
        CREATE TABLE IF NOT EXISTS `crow_logs` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `type` varchar(50) NOT NULL,
            `identifier` varchar(50) DEFAULT NULL,
            `message` text NOT NULL,
            `data` longtext DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `type` (`type`),
            KEY `identifier` (`identifier`),
            KEY `created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ]], {}, function(result)
        if result then
            CrowUtils.Log('info', 'Logs table created/verified')
        else
            CrowUtils.Log('error', 'Failed to create logs table')
        end
    end)
end

-- Migrera databas för att lägga till nya kolumner
function CrowDatabase.MigrateDatabase()
    CrowUtils.Log('info', 'Running database migration...')

    -- Kontrollera om gender kolumn finns
    MySQL.Async.fetchAll([[
        SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'users' AND COLUMN_NAME = 'gender'
    ]], {}, function(result)
        if not result or #result == 0 then
            -- Lägg till gender kolumn
            MySQL.Async.execute([[
                ALTER TABLE `users` ADD COLUMN `gender` varchar(10) DEFAULT NULL
            ]], {}, function(addResult)
                if addResult then
                    CrowUtils.Log('info', 'Gender column added successfully')
                else
                    CrowUtils.Log('error', 'Failed to add gender column')
                end
            end)
        else
            CrowUtils.Log('info', 'Gender column already exists')
        end
    end)

    -- Kontrollera om character_created kolumn finns
    MySQL.Async.fetchAll([[
        SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'users' AND COLUMN_NAME = 'character_created'
    ]], {}, function(result)
        if not result or #result == 0 then
            -- Lägg till character_created kolumn
            MySQL.Async.execute([[
                ALTER TABLE `users` ADD COLUMN `character_created` tinyint(1) NOT NULL DEFAULT 0
            ]], {}, function(addResult)
                if addResult then
                    CrowUtils.Log('info', 'Character_created column added successfully')
                else
                    CrowUtils.Log('error', 'Failed to add character_created column')
                end
            end)
        else
            CrowUtils.Log('info', 'Character_created column already exists')
        end
    end)

    CrowUtils.Log('info', 'Database migration completed')
end

-- Kontrollera om spelare finns
function CrowDatabase.PlayerExists(identifier)
    local result = MySQL.Sync.fetchScalar('SELECT COUNT(*) FROM users WHERE identifier = ?', {identifier})
    return result and result > 0
end

-- Skapa ny spelare
function CrowDatabase.CreatePlayer(identifier, name)
    local firstname = name or 'John'
    local lastname = 'Doe'
    
    -- Dela upp namnet om det innehåller mellanslag
    if name and string.find(name, ' ') then
        local parts = CrowUtils.Split(name, ' ')
        firstname = parts[1] or 'John'
        lastname = parts[2] or 'Doe'
    end
    
    MySQL.Async.execute([[
        INSERT INTO users (identifier, firstname, lastname, money, bank, job, job_grade, `group`, character_created)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ]], {
        identifier,
        firstname,
        lastname,
        Config.Economy.StartingMoney,
        Config.Economy.StartingBank,
        'unemployed',
        0,
        'user',
        0
    }, function(result)
        if result then
            CrowUtils.Log('info', 'Created new player: %s (%s)', name, identifier)
        else
            CrowUtils.Log('error', 'Failed to create player: %s (%s)', name, identifier)
        end
    end)
end

-- Ladda spelardata
function CrowDatabase.LoadPlayer(identifier)
    local result = MySQL.Sync.fetchAll('SELECT * FROM users WHERE identifier = ?', {identifier})
    
    if result and result[1] then
        local data = result[1]
        
        -- Konvertera JSON-fält
        if data.position then
            data.position = json.decode(data.position) or {}
        end
        
        if data.skin then
            data.skin = json.decode(data.skin) or {}
        end
        
        if data.loadout then
            data.loadout = json.decode(data.loadout) or {}
        end
        
        CrowUtils.Log('info', 'Loaded player data for: %s', identifier)
        return data
    end
    
    CrowUtils.Log('error', 'Failed to load player data for: %s', identifier)
    return nil
end

-- Spara spelardata
function CrowDatabase.SavePlayer(source, data)
    if not data or not data.identifier then
        CrowUtils.Log('error', 'Invalid player data for save')
        return false
    end
    
    -- Konvertera tabeller till JSON
    local position = data.position and json.encode(data.position) or nil
    local skin = data.skin and json.encode(data.skin) or nil
    local loadout = data.loadout and json.encode(data.loadout) or nil
    
    MySQL.Async.execute([[
        UPDATE users SET
            firstname = ?, lastname = ?, money = ?, bank = ?,
            job = ?, job_grade = ?, `group` = ?, position = ?,
            skin = ?, loadout = ?, gender = ?, character_created = ?, last_seen = NOW()
        WHERE identifier = ?
    ]], {
        data.firstname or '',
        data.lastname or '',
        data.money or 0,
        data.bank or 0,
        data.job or 'unemployed',
        data.job_grade or 0,
        data.group or 'user',
        position,
        skin,
        loadout,
        data.gender or nil,
        data.character_created or 0,
        data.identifier
    }, function(result)
        if result then
            CrowUtils.Log('info', 'Saved player data for: %s', data.identifier)
        else
            CrowUtils.Log('error', 'Failed to save player data for: %s', data.identifier)
        end
    end)
    
    return true
end

-- Uppdatera spelarpengar
function CrowDatabase.UpdatePlayerMoney(identifier, money, bank)
    MySQL.Async.execute('UPDATE users SET money = ?, bank = ? WHERE identifier = ?', {
        money, bank, identifier
    }, function(result)
        if result then
            CrowUtils.Log('info', 'Updated money for: %s (Cash: %d, Bank: %d)', identifier, money, bank)
        else
            CrowUtils.Log('error', 'Failed to update money for: %s', identifier)
        end
    end)
end

-- Uppdatera spelarjobb
function CrowDatabase.UpdatePlayerJob(identifier, job, grade)
    MySQL.Async.execute('UPDATE users SET job = ?, job_grade = ? WHERE identifier = ?', {
        job, grade, identifier
    }, function(result)
        if result then
            CrowUtils.Log('info', 'Updated job for: %s (%s - Grade %d)', identifier, job, grade)
        else
            CrowUtils.Log('error', 'Failed to update job for: %s', identifier)
        end
    end)
end

-- Logga händelse
function CrowDatabase.CreateLog(type, identifier, message, data)
    local jsonData = data and json.encode(data) or nil
    
    MySQL.Async.execute([[
        INSERT INTO crow_logs (type, identifier, message, data)
        VALUES (?, ?, ?, ?)
    ]], {
        type, identifier, message, jsonData
    }, function(result)
        if not result then
            CrowUtils.Log('error', 'Failed to create log entry')
        end
    end)
end

-- Rensa spelardata (för admin-kommando)
function CrowDatabase.ResetPlayer(identifier)
    MySQL.Async.execute([[
        UPDATE users SET
            firstname = '', lastname = '',
            position = NULL, skin = NULL, loadout = NULL, gender = NULL,
            money = ?, bank = ?, job = 'unemployed', job_grade = 0, character_created = 0
        WHERE identifier = ?
    ]], {
        Config.Economy.StartingMoney,
        Config.Economy.StartingBank,
        identifier
    }, function(result)
        if result then
            CrowUtils.Log('info', 'Reset player data for: %s', identifier)
        else
            CrowUtils.Log('error', 'Failed to reset player data for: %s', identifier)
        end
    end)
end

-- Kontrollera om spelare har skapat karaktär
function CrowDatabase.HasCharacter(identifier)
    local result = MySQL.Sync.fetchScalar('SELECT character_created FROM users WHERE identifier = ?', {identifier})
    return result and result == 1
end

-- Markera karaktär som skapad
function CrowDatabase.SetCharacterCreated(identifier, gender)
    MySQL.Async.execute([[
        UPDATE users SET character_created = 1, gender = ? WHERE identifier = ?
    ]], {
        gender, identifier
    }, function(result)
        if result then
            CrowUtils.Log('info', 'Marked character as created for: %s (Gender: %s)', identifier, gender)
        else
            CrowUtils.Log('error', 'Failed to mark character as created for: %s', identifier)
        end
    end)
end

-- Exportera databas-funktioner
exports('CreateLog', function(type, identifier, message, data)
    CrowDatabase.CreateLog(type, identifier, message, data)
end)

exports('ResetPlayer', function(identifier)
    CrowDatabase.ResetPlayer(identifier)
end)

exports('HasCharacter', function(identifier)
    return CrowDatabase.HasCharacter(identifier)
end)

exports('SetCharacterCreated', function(identifier, gender)
    CrowDatabase.SetCharacterCreated(identifier, gender)
end)
