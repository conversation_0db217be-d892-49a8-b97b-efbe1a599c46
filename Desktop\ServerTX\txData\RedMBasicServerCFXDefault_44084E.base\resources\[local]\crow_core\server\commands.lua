-- Crow Framework - Commands

-- Reset player character command
RegisterCommand('resetcharacter', function(source, args, rawCommand)
    local player = CrowCore.GetPlayer(source)

    if not player or (player.group ~= 'admin' and player.group ~= 'superadmin') then
        TriggerClientEvent('crow_core:notify', source, 'You don\'t have permission for this command', 'error')
        return
    end

    if not args[1] then
        TriggerClientEvent('crow_core:notify', source, 'Usage: /resetcharacter [player_id]', 'error')
        return
    end

    local targetId = tonumber(args[1])
    local targetPlayer = CrowCore.GetPlayer(targetId)

    if not targetPlayer then
        TriggerClientEvent('crow_core:notify', source, 'Player not found', 'error')
        return
    end

    -- Reset player data
    CrowDatabase.ResetPlayer(targetPlayer.identifier)

    -- Kick player to force reconnect
    DropPlayer(targetId, 'Your character has been reset. Please reconnect to create a new character.')

    TriggerClientEvent('crow_core:notify', source, 'Player character reset successfully', 'success')
    CrowUtils.Log('info', 'Admin %s reset character for player %s', player.identifier, targetPlayer.identifier)
end, false)

-- Reset own character command (for testing)
RegisterCommand('resetmycharacter', function(source, args, rawCommand)
    local player = CrowCore.GetPlayer(source)

    if not player then
        return
    end

    -- Reset player data
    CrowDatabase.ResetPlayer(player.identifier)

    -- Kick player to force reconnect
    DropPlayer(source, 'Your character has been reset. Please reconnect to create a new character.')

    CrowUtils.Log('info', 'Player %s reset their own character', player.identifier)
end, false)

-- Database migration command
RegisterCommand('migratedb', function(source, args, rawCommand)
    local player = CrowCore.GetPlayer(source)

    if not player or player.group ~= 'superadmin' then
        TriggerClientEvent('crow_core:notify', source, 'You don\'t have permission for this command (superadmin only)', 'error')
        return
    end

    CrowDatabase.MigrateDatabase()
    TriggerClientEvent('crow_core:notify', source, 'Database migration started - check console for results', 'info')
end, false)

-- Reset all characters command (DANGEROUS - for development only)
RegisterCommand('resetallcharacters', function(source, args, rawCommand)
    local player = CrowCore.GetPlayer(source)

    if not player or player.group ~= 'superadmin' then
        TriggerClientEvent('crow_core:notify', source, 'You don\'t have permission for this command (superadmin only)', 'error')
        return
    end

    -- Confirm command
    if not args[1] or args[1] ~= 'CONFIRM' then
        TriggerClientEvent('crow_core:notify', source, 'This will reset ALL player characters! Use: /resetallcharacters CONFIRM', 'error')
        return
    end

    -- Reset all player data
    MySQL.Async.execute([[
        UPDATE users SET
            firstname = '', lastname = '',
            position = NULL, skin = NULL, loadout = NULL, gender = NULL,
            money = ?, bank = ?, job = 'unemployed', job_grade = 0, character_created = 0
    ]], {
        Config.Economy.StartingMoney,
        Config.Economy.StartingBank
    }, function(result)
        if result then
            TriggerClientEvent('crow_core:notify', source, 'All characters have been reset!', 'success')
            CrowUtils.Log('info', 'Superadmin %s reset ALL characters', player.identifier)

            -- Kick all players to force reconnect
            local players = GetPlayers()
            for _, playerId in ipairs(players) do
                if tonumber(playerId) ~= source then
                    DropPlayer(playerId, 'All characters have been reset. Please reconnect to create a new character.')
                end
            end
        else
            TriggerClientEvent('crow_core:notify', source, 'Failed to reset characters', 'error')
        end
    end)
end, false)

-- Admin kommandon
CrowFramework.RegisterCommand({'givemoney', 'gm'}, 'admin', function(source, args, rawCommand, player)
    if #args < 2 then
        player.showNotification({
            type = 'error',
            title = 'Fel',
            message = 'Användning: /givemoney [id] [belopp]'
        })
        return
    end
    
    local targetId = tonumber(args[1])
    local amount = tonumber(args[2])
    
    if not targetId or not amount then
        player.showNotification({
            type = 'error',
            title = 'Fel',
            message = 'Ogiltiga argument'
        })
        return
    end
    
    local targetPlayer = CrowFramework.GetPlayer(targetId)
    if not targetPlayer then
        player.showNotification({
            type = 'error',
            title = 'Fel',
            message = 'Spelare hittades inte'
        })
        return
    end
    
    if targetPlayer.addMoney(amount, 'Admin gave money') then
        player.showNotification({
            type = 'success',
            title = 'Framgång',
            message = string.format('Gav %s till %s', CrowUtils.FormatMoney(amount), targetPlayer.getName())
        })
        
        targetPlayer.showNotification({
            type = 'success',
            title = 'Pengar mottagna',
            message = string.format('Du fick %s från en admin', CrowUtils.FormatMoney(amount))
        })
    else
        player.showNotification({
            type = 'error',
            title = 'Fel',
            message = 'Kunde inte ge pengar'
        })
    end
end, {
    help = 'Ge pengar till en spelare',
    arguments = {
        {name = 'id', help = 'Spelare ID'},
        {name = 'belopp', help = 'Pengar att ge'}
    }
})

CrowFramework.RegisterCommand({'givebank', 'gb'}, 'admin', function(source, args, rawCommand, player)
    if #args < 2 then
        player.showNotification({
            type = 'error',
            title = 'Fel',
            message = 'Användning: /givebank [id] [belopp]'
        })
        return
    end
    
    local targetId = tonumber(args[1])
    local amount = tonumber(args[2])
    
    if not targetId or not amount then
        player.showNotification({
            type = 'error',
            title = 'Fel',
            message = 'Ogiltiga argument'
        })
        return
    end
    
    local targetPlayer = CrowFramework.GetPlayer(targetId)
    if not targetPlayer then
        player.showNotification({
            type = 'error',
            title = 'Fel',
            message = 'Spelare hittades inte'
        })
        return
    end
    
    if targetPlayer.addBank(amount, 'Admin gave bank money') then
        player.showNotification({
            type = 'success',
            title = 'Framgång',
            message = string.format('Gav %s till %s (bank)', CrowUtils.FormatMoney(amount), targetPlayer.getName())
        })
        
        targetPlayer.showNotification({
            type = 'success',
            title = 'Bankpengar mottagna',
            message = string.format('Du fick %s på banken från en admin', CrowUtils.FormatMoney(amount))
        })
    else
        player.showNotification({
            type = 'error',
            title = 'Fel',
            message = 'Kunde inte ge bankpengar'
        })
    end
end, {
    help = 'Ge bankpengar till en spelare',
    arguments = {
        {name = 'id', help = 'Spelare ID'},
        {name = 'belopp', help = 'Bankpengar att ge'}
    }
})

CrowFramework.RegisterCommand({'setjob', 'job'}, 'admin', function(source, args, rawCommand, player)
    if #args < 2 then
        player.showNotification({
            type = 'error',
            title = 'Fel',
            message = 'Användning: /setjob [id] [jobb] [grad]'
        })
        return
    end
    
    local targetId = tonumber(args[1])
    local job = args[2]
    local grade = tonumber(args[3]) or 0
    
    if not targetId or not job then
        player.showNotification({
            type = 'error',
            title = 'Fel',
            message = 'Ogiltiga argument'
        })
        return
    end
    
    local targetPlayer = CrowFramework.GetPlayer(targetId)
    if not targetPlayer then
        player.showNotification({
            type = 'error',
            title = 'Fel',
            message = 'Spelare hittades inte'
        })
        return
    end
    
    if targetPlayer.setJob(job, grade) then
        local jobData = targetPlayer.getJob()
        
        player.showNotification({
            type = 'success',
            title = 'Framgång',
            message = string.format('Satte %s som %s', targetPlayer.getName(), jobData.label)
        })
        
        targetPlayer.showNotification({
            type = 'success',
            title = 'Nytt jobb',
            message = string.format('Du är nu %s', jobData.label)
        })
    else
        player.showNotification({
            type = 'error',
            title = 'Fel',
            message = 'Ogiltigt jobb eller grad'
        })
    end
end, {
    help = 'Sätt jobb för en spelare',
    arguments = {
        {name = 'id', help = 'Spelare ID'},
        {name = 'jobb', help = 'Jobbnamn'},
        {name = 'grad', help = 'Jobbgrad (valfritt)'}
    }
})

CrowFramework.RegisterCommand({'setgroup', 'group'}, 'superadmin', function(source, args, rawCommand, player)
    if #args < 2 then
        player.showNotification({
            type = 'error',
            title = 'Fel',
            message = 'Användning: /setgroup [id] [grupp]'
        })
        return
    end
    
    local targetId = tonumber(args[1])
    local group = args[2]
    
    if not targetId or not group then
        player.showNotification({
            type = 'error',
            title = 'Fel',
            message = 'Ogiltiga argument'
        })
        return
    end
    
    local targetPlayer = CrowFramework.GetPlayer(targetId)
    if not targetPlayer then
        player.showNotification({
            type = 'error',
            title = 'Fel',
            message = 'Spelare hittades inte'
        })
        return
    end
    
    if targetPlayer.setGroup(group) then
        player.showNotification({
            type = 'success',
            title = 'Framgång',
            message = string.format('Satte %s som %s', targetPlayer.getName(), group)
        })
        
        targetPlayer.showNotification({
            type = 'info',
            title = 'Grupp ändrad',
            message = string.format('Din grupp är nu: %s', group)
        })
    else
        player.showNotification({
            type = 'error',
            title = 'Fel',
            message = 'Ogiltig grupp'
        })
    end
end, {
    help = 'Sätt grupp för en spelare',
    arguments = {
        {name = 'id', help = 'Spelare ID'},
        {name = 'grupp', help = 'Gruppnamn'}
    }
})

-- Användarkommandon
CrowFramework.RegisterCommand({'me', 'mig'}, 'user', function(source, args, rawCommand, player)
    if #args == 0 then
        player.showNotification({
            type = 'error',
            title = 'Fel',
            message = 'Användning: /me [handling]'
        })
        return
    end
    
    local message = table.concat(args, ' ')
    local playerName = player.getName()
    
    -- Skicka till alla spelare i närheten (kan utökas med distanscheck)
    TriggerClientEvent('chat:addMessage', -1, {
        template = '<div style="padding: 0.5vw; margin: 0.5vw; background-color: rgba(255, 165, 0, 0.1); border-left: 3px solid orange;"><b>{0}</b> {1}</div>',
        args = {playerName, message}
    })
end, {
    help = 'Utför en handling',
    arguments = {
        {name = 'handling', help = 'Vad du gör'}
    }
})

CrowFramework.RegisterCommand({'money', 'pengar'}, 'user', function(source, args, rawCommand, player)
    local money = player.getMoney()
    local bank = player.getBank()
    
    player.showNotification({
        type = 'info',
        title = 'Dina pengar',
        message = string.format('Kontanter: %s\nBank: %s', CrowUtils.FormatMoney(money), CrowUtils.FormatMoney(bank)),
        duration = 8000
    })
end, {
    help = 'Visa dina pengar'
})

CrowFramework.RegisterCommand({'job', 'jobb'}, 'user', function(source, args, rawCommand, player)
    local job = player.getJob()
    
    player.showNotification({
        type = 'info',
        title = 'Ditt jobb',
        message = string.format('%s\nGrad: %s\nLön: %s', job.label, job.grade_label, CrowUtils.FormatMoney(job.salary)),
        duration = 8000
    })
end, {
    help = 'Visa ditt jobb'
})
