-- Custom Banking System - Server
local playerData = {}

-- Hämta spelardata från databas
function GetPlayerData(source)
    local identifier = GetPlayerIdentifier(source, 0)
    if not identifier then return nil end
    
    if playerData[source] then
        return playerData[source]
    end
    
    local result = MySQL.Sync.fetchAll('SELECT * FROM ' .. Config.Database.table .. ' WHERE ' .. Config.Database.identifierColumn .. ' = ?', {identifier})
    
    if result and result[1] then
        playerData[source] = {
            identifier = identifier,
            money = result[1][Config.Database.moneyColumn] or 0,
            bank = result[1][Config.Database.bankColumn] or 0
        }
        return playerData[source]
    end
    
    return nil
end

-- Uppdatera spelardata i databas
function UpdatePlayerMoney(source, money, bank)
    local data = GetPlayerData(source)
    if not data then return false end
    
    data.money = money
    data.bank = bank
    
    MySQL.Async.execute('UPDATE ' .. Config.Database.table .. ' SET ' .. Config.Database.moneyColumn .. ' = ?, ' .. Config.Database.bankColumn .. ' = ? WHERE ' .. Config.Database.identifierColumn .. ' = ?', {
        money, bank, data.identifier
    })
    
    return true
end

-- Hämta spelarens pengar
RegisterServerEvent('custom_banking:getPlayerMoney')
AddEventHandler('custom_banking:getPlayerMoney', function()
    local source = source
    local data = GetPlayerData(source)
    
    if data then
        TriggerClientEvent('custom_banking:receivePlayerMoney', source, data.money, data.bank)
    end
end)

-- Sätt in pengar
RegisterServerEvent('custom_banking:deposit')
AddEventHandler('custom_banking:deposit', function(amount)
    local source = source
    local data = GetPlayerData(source)
    
    if not data then return end
    
    amount = tonumber(amount)
    if not amount or amount <= 0 or amount > data.money then
        TriggerClientEvent('custom_banking:notify', source, _L('insufficient_funds'), 'error')
        return
    end
    
    local newMoney = data.money - amount
    local newBank = data.bank + amount
    
    if UpdatePlayerMoney(source, newMoney, newBank) then
        TriggerClientEvent('custom_banking:notify', source, _L('transaction_success'), 'success')
        TriggerClientEvent('custom_banking:receivePlayerMoney', source, newMoney, newBank)
        
        -- Logga transaktion
        print(string.format('[BANKING] %s deposited $%d', GetPlayerName(source), amount))
    end
end)

-- Ta ut pengar
RegisterServerEvent('custom_banking:withdraw')
AddEventHandler('custom_banking:withdraw', function(amount)
    local source = source
    local data = GetPlayerData(source)
    
    if not data then return end
    
    amount = tonumber(amount)
    if not amount or amount <= 0 or amount > data.bank then
        TriggerClientEvent('custom_banking:notify', source, _L('insufficient_funds'), 'error')
        return
    end
    
    local newMoney = data.money + amount
    local newBank = data.bank - amount
    
    if UpdatePlayerMoney(source, newMoney, newBank) then
        TriggerClientEvent('custom_banking:notify', source, _L('transaction_success'), 'success')
        TriggerClientEvent('custom_banking:receivePlayerMoney', source, newMoney, newBank)
        
        -- Logga transaktion
        print(string.format('[BANKING] %s withdrew $%d', GetPlayerName(source), amount))
    end
end)

-- Överför pengar
RegisterServerEvent('custom_banking:transfer')
AddEventHandler('custom_banking:transfer', function(targetId, amount)
    local source = source
    local target = tonumber(targetId)
    local data = GetPlayerData(source)
    local targetData = GetPlayerData(target)
    
    if not data or not targetData then
        TriggerClientEvent('custom_banking:notify', source, 'Spelare hittades inte', 'error')
        return
    end
    
    amount = tonumber(amount)
    if not amount or amount <= 0 then
        TriggerClientEvent('custom_banking:notify', source, _L('invalid_amount'), 'error')
        return
    end
    
    local fee = math.floor(amount * Config.Settings.transferFee)
    local totalAmount = amount + fee
    
    if totalAmount > data.bank then
        TriggerClientEvent('custom_banking:notify', source, _L('insufficient_funds'), 'error')
        return
    end
    
    if amount > Config.Settings.maxTransfer then
        TriggerClientEvent('custom_banking:notify', source, 'Överföring för stor (Max: $' .. Config.Settings.maxTransfer .. ')', 'error')
        return
    end
    
    -- Utför överföring
    local newSenderBank = data.bank - totalAmount
    local newTargetBank = targetData.bank + amount
    
    if UpdatePlayerMoney(source, data.money, newSenderBank) and UpdatePlayerMoney(target, targetData.money, newTargetBank) then
        TriggerClientEvent('custom_banking:notify', source, _L('transaction_success') .. ' (Avgift: $' .. fee .. ')', 'success')
        TriggerClientEvent('custom_banking:notify', target, 'Du fick $' .. amount .. ' från ' .. GetPlayerName(source), 'success')
        
        TriggerClientEvent('custom_banking:receivePlayerMoney', source, data.money, newSenderBank)
        TriggerClientEvent('custom_banking:receivePlayerMoney', target, targetData.money, newTargetBank)
        
        -- Logga transaktion
        print(string.format('[BANKING] %s transferred $%d to %s (fee: $%d)', GetPlayerName(source), amount, GetPlayerName(target), fee))
    end
end)

-- Rensa spelardata när spelare lämnar
AddEventHandler('playerDropped', function()
    local source = source
    if playerData[source] then
        playerData[source] = nil
    end
end)
